RegisterServerEvent('chat:init')
RegisterServerEvent('chat:addTemplate')
RegisterServerEvent('chat:addMessage')
RegisterServerEvent('chat:addSuggestion')
RegisterServerEvent('chat:removeSuggestion')
RegisterServerEvent('_chat:messageEntered')
RegisterServerEvent('chat:clear')
RegisterServerEvent('__cfx_internal:commandFallback')
RegisterServerEvent('x:chatMessage')

-- Handle regular chat messages
AddEventHandler('chatMessage', function(source, name, message)
    local playerId = source
    local nameWithId = name .. ' [' .. playerId .. ']'

    -- Broadcast to all clients with player ID
    TriggerClientEvent('chatMessage', -1, nameWithId, { 255, 255, 255 }, message)
end)

-- Handle x:chatMessage events
AddEventHandler('x:chatMessage', function(title, author, color, text, systemMessage, systemColour)
    local playerId = source
    local authorWithId = author .. ' [' .. playerId .. ']'

    -- Broadcast to all clients with player ID
    TriggerClientEvent('x:chatMessage', -1, title, authorWithId, color, text, systemMessage, systemColour)
end)


AddEventHandler('__cfx_internal:commandFallback', function(command)
    local name = GetPlayerName(source)
    local playerId = source
    local nameWithId = name .. ' [' .. playerId .. ']'

    TriggerEvent('chatMessage', source, nameWithId, '/' .. command)

    if not WasEventCanceled() then
        TriggerClientEvent('chatMessage', -1, nameWithId, { 255, 255, 255 }, '' .. command)
    end

    CancelEvent()
end)

-- command suggestions for clients
local function refreshCommands(player)
    if GetRegisteredCommands then
        local registeredCommands = GetRegisteredCommands()

        local suggestions = {}

        for _, command in ipairs(registeredCommands) do
            if IsPlayerAceAllowed(player, ('command.%s'):format(command.name)) then
                table.insert(suggestions, {
                    name = '/' .. command.name,
                    help = ''
                })
            end
        end

        TriggerClientEvent('chat:addSuggestions', player, suggestions)
    end
end

AddEventHandler('chat:init', function()
    refreshCommands(source)
end)

AddEventHandler('onServerResourceStart', function(resName)
    Wait(500)

    for _, player in ipairs(GetPlayers()) do
        refreshCommands(player)
    end
end)
