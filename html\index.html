<!DOCTYPE html>

<html>

<head>

  <meta charset="utf-8">
  <title></title>

  <script src="https://kit.fontawesome.com/681776800a.js" crossorigin="anonymous"></script>
  <link href="vendor/latofonts.css" rel="stylesheet">
  <link href="vendor/flexboxgrid.6.3.1.min.css" rel="stylesheet"></link>
  <link href="vendor/animate.3.5.2.min.css" rel="stylesheet"></link>
  <link href="index.css" rel="stylesheet"></link>

  <script src="vendor/vue.2.3.3.min.js" type="text/javascript"></script>
  <script src="config.default.js" type="text/javascript"></script>
  <script src="config.js" type="text/javascript"></script>
</head>

<body>

  <div id="app"></div>



  <!-- App Template -->

  <script type="text/x-template" id="app_template">

    <div id="app">

      <div class="chat-window" :class="{ 'fadeOut animated': !showWindow, 'hidden': shouldHide }">

        <div class="chat-messages" ref="messages">

          <message v-for="msg in messages"

              :templates="templates"
              :multiline="msg.multiline"
              :title="msg.title"
              :args="msg.args"
              :systemMessage="msg.systemMessage"
              :systemColour="msg.systemColour"
              :color="msg.color"
              :template="msg.template"
              :template-id="msg.templateId"
              :key="msg">

          </message>
          
        </div>

      </div>

      <div class="chat-input" v-show="showInput">

        <div>

          <span class="prefix">➤</span>

          <textarea v-model="message"

                    ref="input"

                    type="text"

                    autofocus

                    spellcheck="false"

                    @keyup.esc="hideInput"

                    @keyup="keyUp"

                    @keydown="keyDown"

                    @keypress.enter.prevent="send">

          </textarea>

        </div>

        <suggestions :message="message" :suggestions="suggestions">

        </suggestions>

      </div>

    </div>

  </script>



  <!-- Message Template -->

  <script type="text/x-template" id="message_template">
    <div class="msg" :class="{ 'sysmessage' : systemMessage === true, multiline }">
      <span v-html="textEscaped"></span>
    </div>
  </script>

  <!-- Suggestions Template -->

  <script type="text/x-template" id="suggestions_template">

    <div class="suggestions-wrap" v-show="currentSuggestions.length > 0">

      <ul class="suggestions">

        <li class="suggestion" v-for="s in currentSuggestions">

          <p>

            <span :class="{ 'disabled': s.disabled }">

              {{s.name}}

            </span>

            <span class="param"

                  v-for="(p, index) in s.params"

                  :class="{ 'disabled': p.disabled }">

              [{{p.name}}]

            </span>

          </p>

          <small class="help">

            <template v-if="!s.disabled">

              {{s.help}}

            </template>

            <template v-for="p in s.params" v-if="!p.disabled">

              {{p.help}}

            </template>

          </small>

        </li>

      </ul>

    </div>

  </script>



  <!-- Scripts -->

  <script type="text/javascript" src="./Suggestions.js"></script>

  <script type="text/javascript" src="./Message.js"></script>

  <script type="text/javascript" src="./App.js"></script>



  <!-- Main Entry -->

  <script type="text/javascript">
    window.post = (url, data) => {

      var request = new XMLHttpRequest();

      request.open('POST', url, true);

      request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');

      request.send(data);

    }



    const instance = new Vue({

      el: '#app',

      render: h => h(APP),

    });



    window.emulate = (type, detail = {}) => {

      detail.type = type;

      window.dispatchEvent(new CustomEvent('message', {

        detail,

      }));

    };
  </script>



</body>

</html>