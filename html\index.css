
.color-0 {
  color: #ffffff;
}
.color-1 {
  color: #ff4444;
}
.color-2 {
  color: #99cc00;
}
.color-3 {
  color: #ffbb33;
}
.color-4 {
  color: #0099cc;
}
.color-5 {
  color: #33b5e5;
}
.color-6 {
  color: #aa66cc;
}
.color-8 {
  color: #cc0000;
}
.color-9 {
  color: #cc0068;
}

* {
  font-family: 'Lato', sans-serif;
  margin: 0;
  padding: 0;
}

body {
  margin: 12px;
}

.no-grow {
  flex-grow: 0;
}

em {
  font-style: normal;
}

#app {
  font-family: 'Lato', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: white;
}

.chat-window {
  position: absolute;
  top: 1.5%;
  left: 0.8%;
  width: 28%;
  height: 22%;
  max-width: 1000px;
  background-color: rgba(52, 73, 94, 0.7);
  -webkit-animation-duration: 2s;
}

.chat-messages {
  position: relative;
  top: 70%;
  height: 95%;
  font-size: 1.8vh;
  margin: 1%;

  overflow-x: hidden;
  overflow-y: hidden;
}

.sysmessage {
  font-size: 1.3vh !important;
  color: rgb(187, 187, 187) !important;
}

.chat-input {
  font-size: 1.65vh;
  position: absolute;

  margin-top: 12.5%;
  top: 58%;
  left: 0.8%;
  width: 28%;
  max-width: 1000px;
  box-sizing: border-box;
}

.prefix {
  font-size: 1.8vh;
  position: absolute;
  margin-top: 0.5%;
  left: 0.208%;
}

textarea {
  font-size: 1.65vh;
  display: block;
  box-sizing: border-box;
  padding: 1%;
  padding-left: 3.5%;
  color: white;
  background-color: rgba(44, 62, 80, 1.0);
  width: 100%;
  border-width: 0;
  height: 3.15%;
  overflow: hidden;
  text-overflow: ellipsis;
}

textarea:focus,
input:focus {
  outline: none;
}

.multiline {
  margin-left: 4%;
  text-indent: -1.2rem;
  white-space: pre-line;
}

.suggestions {
  list-style-type: none;
  padding: 0.5%;
  padding-left: 1.4%;
  font-size: 1.5vh;
  box-sizing: border-box;
  color: white;
  background-color: rgba(44, 62, 80, 1.0);
  width: 100%;
}

.help {
  color: #b0bbbd;
}

.disabled {
  color: #b0bbbd;
}

.suggestion {
  margin-bottom: 0.5%;
}

.hidden {
  display: none;
}