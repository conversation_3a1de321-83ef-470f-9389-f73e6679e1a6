* {
    font-family: inherit;
}

.chat-window {
    --size: calc(((2.7vh * 1.2)) * 10);

    position: absolute;
    left: 10px;
    top: calc(24% - (var(--size) / 2));
    width: 28%;
    height: var(--size) !important;

    background: inherit !important;

    text-align: left;
    user-select: none;
}

.headtesting {
    font-family: Font2_cond, sans-serif;
    font-size: 4.7px;  /* Reduced font size by 0.3 */
    padding: 2px 5px;
    margin-bottom: 5px;
    margin-right: 5px;
    text-transform: uppercase;
    color: white;
    z-index: 3;
}

@font-face {
    font-family: 'Font2';
    src: url(https://runtime.fivem.net/temp/ChaletLondonNineteenSixty.otf?a);
}

@font-face {
    font-family: 'Font2_cond';
    src: url(https://runtime.fivem.net/temp/chaletcomprime-colognesixty-webfont.ttf?a);
}

.msg {
    font-family: Font2, sans-serif;
    color: #fff;

    font-size: calc(1.1vh);  /* Smaller text size for better readability */
    filter: url(#svgDropShadowFilter);

    line-height: calc(1.2vh * 1.0);  /* Tighter line height */
    margin-bottom: 0.05%;  /* Very minimal margin between messages */
    margin-top: 0;  /* Remove top margin */
    padding: 0;  /* Remove padding */
}

.sysmessage {
    font-size: 0.7vh !important;  /* Reduced font size by 0.3 */
    color: rgb(219, 219, 219) !important;
    line-height: calc(0.8vh * 1.0);  /* Further reduced line height for tighter spacing */
    margin-bottom: 0.05%;  /* Minimal margin for system messages */
}

.sysmessage b {
    font-size: 1.4vh !important;  /* Reduced font size by 0.3 */
}

.chat-messages {
    margin: 0;
    padding: 0;  /* Remove padding */
    height: 100%;
}

.msg > span > span > b {
    font-family: Font2_cond, sans-serif;
    font-weight: normal;

    vertical-align: baseline;
    padding-right: 5px;  /* Reduced padding for closer text */

    line-height: 1;

    font-size: calc(1.3vh);  /* Smaller player name text */
}

.msg > span > span > span {
    vertical-align: baseline;
    margin-left: 0;  /* Remove any left margin */
    padding-left: 0;  /* Remove any left padding */
}

.msg > span {
    margin: 0;  /* Remove margin from message spans */
    padding: 0;  /* Remove padding from message spans */
}

.msg i:first-of-type {
    font-style: normal;
    color: #c0c0c0;
}

.chat-input {
    position: absolute;
    left: 10px;
    top: 41%;

    background: inherit !important;

    text-align: right;

    bottom: auto;

    height: auto;

    font-family: Font2, sans-serif;
}

.chat-input > div {
    background-color: rgba(0, 0, 0, .6);
    padding: calc(0.28vh / 2);
}

.chat-input .prefix {
    margin: 0;
    margin-left: 0.7%;
    margin-top: -0.1%;
}

.chat-input > div + div {
    position: absolute;
    top: calc(1.35vh + 0.28vh + 0.28vh + 0.28vh + (0.28vh / 2));  /* Reduced the value by 0.3 */
    width: 99.6%;

    text-align: left;
}

.suggestions {
    border: calc(0.28vh / 2) solid rgba(180, 180, 180, .6);
    background: transparent;
}

textarea {
    background: transparent;
    padding: calc(0.18vh / 2);
    padding-left: calc(5% + (0.28vh / 2));
}

@media screen and (min-aspect-ratio: 21/9) {
	.chat-window, .chat-input {
		right: calc(12.8vw);
	}
}

@media screen and (min-aspect-ratio: 32/9) {
	.chat-window, .chat-input {
		right: calc(25vw);
	}
}
